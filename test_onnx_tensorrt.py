#!/usr/bin/env python3
"""
Test script for ONNX and TensorRT models
Tests both ONNX and TensorRT versions of RT-MonoDepth models
"""

import argparse
import os
import time
import numpy as np
import torch
import PIL.Image as pil
from torchvision import transforms
import matplotlib.pyplot as plt
import matplotlib.cm as cm


def load_and_preprocess_image(image_path, height=192, width=640):
    """Load and preprocess image for inference"""
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image not found: {image_path}")
    
    # Load image
    input_image = pil.open(image_path).convert('RGB')
    original_width, original_height = input_image.size
    
    # Resize to model input size
    input_image = input_image.resize((width, height), pil.LANCZOS)
    
    # Convert to tensor and normalize
    input_tensor = transforms.ToTensor()(input_image).unsqueeze(0)
    
    return input_tensor, (original_width, original_height)


def test_onnx_model(onnx_path, image_path, output_dir):
    """Test ONNX model inference"""
    try:
        import onnxruntime as ort
    except ImportError:
        print("onnxruntime not installed. Install with: pip install onnxruntime")
        return False
    
    print(f"Testing ONNX model: {onnx_path}")
    
    # Create ONNX Runtime session
    try:
        ort_session = ort.InferenceSession(onnx_path)
    except Exception as e:
        print(f"Failed to load ONNX model: {e}")
        return False
    
    # Load and preprocess image
    input_tensor, original_size = load_and_preprocess_image(image_path)
    input_numpy = input_tensor.numpy()
    
    # Warm up
    print("Warming up ONNX model...")
    for _ in range(10):
        ort_inputs = {ort_session.get_inputs()[0].name: input_numpy}
        _ = ort_session.run(None, ort_inputs)
    
    # Benchmark
    print("Benchmarking ONNX model...")
    times = []
    for i in range(100):
        start_time = time.time()
        ort_inputs = {ort_session.get_inputs()[0].name: input_numpy}
        ort_outputs = ort_session.run(None, ort_inputs)
        end_time = time.time()
        times.append(end_time - start_time)
        
        if (i + 1) % 20 == 0:
            print(f"  Completed {i + 1}/100 iterations")
    
    # Calculate statistics
    mean_time = np.mean(times) * 1000  # Convert to ms
    std_time = np.std(times) * 1000
    fps = 1.0 / np.mean(times)
    
    print(f"ONNX Results:")
    print(f"  Mean inference time: {mean_time:.2f} ± {std_time:.2f} ms")
    print(f"  FPS: {fps:.2f}")
    
    # Save output
    if output_dir:
        output = ort_outputs[0]
        save_depth_output(output, image_path, output_dir, "onnx", original_size)
    
    return True


def test_tensorrt_model(engine_path, image_path, output_dir):
    """Test TensorRT model inference"""
    try:
        import tensorrt as trt
        import pycuda.driver as cuda
        import pycuda.autoinit
    except ImportError:
        print("TensorRT Python bindings not installed.")
        print("Install with: pip install tensorrt pycuda")
        return False
    
    print(f"Testing TensorRT model: {engine_path}")
    
    # Load TensorRT engine
    try:
        with open(engine_path, 'rb') as f:
            engine_data = f.read()
        
        runtime = trt.Runtime(trt.Logger(trt.Logger.WARNING))
        engine = runtime.deserialize_cuda_engine(engine_data)
        context = engine.create_execution_context()
    except Exception as e:
        print(f"Failed to load TensorRT engine: {e}")
        return False
    
    # Allocate buffers
    input_tensor, original_size = load_and_preprocess_image(image_path)
    input_numpy = input_tensor.numpy().astype(np.float32)
    
    # Get input/output shapes
    input_shape = input_numpy.shape
    output_shape = (1, 1, 192, 640)  # Assuming depth output
    
    # Allocate device memory
    d_input = cuda.mem_alloc(input_numpy.nbytes)
    d_output = cuda.mem_alloc(np.prod(output_shape) * np.dtype(np.float32).itemsize)
    
    # Create stream
    stream = cuda.Stream()
    
    # Warm up
    print("Warming up TensorRT model...")
    for _ in range(10):
        cuda.memcpy_htod_async(d_input, input_numpy, stream)
        context.execute_async_v2(bindings=[int(d_input), int(d_output)], stream_handle=stream.handle)
        output_numpy = np.empty(output_shape, dtype=np.float32)
        cuda.memcpy_dtoh_async(output_numpy, d_output, stream)
        stream.synchronize()
    
    # Benchmark
    print("Benchmarking TensorRT model...")
    times = []
    for i in range(100):
        start_time = time.time()
        
        cuda.memcpy_htod_async(d_input, input_numpy, stream)
        context.execute_async_v2(bindings=[int(d_input), int(d_output)], stream_handle=stream.handle)
        output_numpy = np.empty(output_shape, dtype=np.float32)
        cuda.memcpy_dtoh_async(output_numpy, d_output, stream)
        stream.synchronize()
        
        end_time = time.time()
        times.append(end_time - start_time)
        
        if (i + 1) % 20 == 0:
            print(f"  Completed {i + 1}/100 iterations")
    
    # Calculate statistics
    mean_time = np.mean(times) * 1000  # Convert to ms
    std_time = np.std(times) * 1000
    fps = 1.0 / np.mean(times)
    
    print(f"TensorRT Results:")
    print(f"  Mean inference time: {mean_time:.2f} ± {std_time:.2f} ms")
    print(f"  FPS: {fps:.2f}")
    
    # Save output
    if output_dir:
        save_depth_output(output_numpy, image_path, output_dir, "tensorrt", original_size)
    
    return True


def save_depth_output(output, image_path, output_dir, model_type, original_size):
    """Save depth output as colorized image"""
    os.makedirs(output_dir, exist_ok=True)
    
    # Get disparity map
    disp = output[0, 0]  # Remove batch and channel dimensions
    
    # Resize to original image size
    disp_resized = np.array(pil.fromarray(disp).resize(original_size, pil.LANCZOS))
    
    # Normalize and colorize
    vmax = np.percentile(disp_resized, 95)
    normalizer = plt.Normalize(vmin=disp_resized.min(), vmax=vmax)
    mapper = cm.ScalarMappable(norm=normalizer, cmap='magma')
    colormapped_im = (mapper.to_rgba(disp_resized)[:, :, :3] * 255).astype(np.uint8)
    
    # Save image
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    output_path = os.path.join(output_dir, f"{image_name}_{model_type}_depth.png")
    pil.fromarray(colormapped_im).save(output_path)
    
    print(f"  Saved depth output: {output_path}")


def main():
    parser = argparse.ArgumentParser(description='Test ONNX and TensorRT models')
    parser.add_argument('--onnx_path', type=str, help='Path to ONNX model')
    parser.add_argument('--engine_path', type=str, help='Path to TensorRT engine')
    parser.add_argument('--image_path', type=str, required=True,
                        help='Path to test image')
    parser.add_argument('--output_dir', type=str, default='./test_outputs',
                        help='Directory to save test outputs')
    
    args = parser.parse_args()
    
    if not args.onnx_path and not args.engine_path:
        print("Please provide either --onnx_path or --engine_path (or both)")
        return
    
    print(f"Testing with image: {args.image_path}")
    print(f"Output directory: {args.output_dir}")
    print()
    
    # Test ONNX model
    if args.onnx_path:
        success = test_onnx_model(args.onnx_path, args.image_path, args.output_dir)
        if not success:
            print("ONNX test failed")
        print()
    
    # Test TensorRT model
    if args.engine_path:
        success = test_tensorrt_model(args.engine_path, args.image_path, args.output_dir)
        if not success:
            print("TensorRT test failed")
        print()
    
    print("Testing completed!")


if __name__ == '__main__':
    main()
