# RT-MonoDepth ONNX and TensorRT Conversion Guide

This guide provides scripts and instructions for converting RT-MonoDepth models to ONNX and TensorRT formats for optimized inference.

## 📁 Files Overview

- `convert_to_onnx.py` - Convert PyTorch models to ONNX format
- `convert_to_tensorrt.py` - Convert ONNX models to TensorRT engines (Python)
- `trtexec_convert.sh` - Convert ONNX models to TensorRT engines (Bash script using trtexec)
- `test_onnx_tensorrt.py` - Test and benchmark ONNX and TensorRT models
- `run_conversion_pipeline.py` - Complete conversion pipeline script

## 🔧 Prerequisites

### Required Dependencies
```bash
# PyTorch and torchvision
pip install torch torchvision

# ONNX support
pip install onnx onnxruntime

# For TensorRT (optional, for testing)
pip install tensorrt pycuda

# Other dependencies
pip install numpy pillow matplotlib
```

### TensorRT Installation
For TensorRT conversion, you need to install TensorRT:
- Download from [NVIDIA TensorRT](https://developer.nvidia.com/tensorrt)
- Follow the installation guide for your platform
- Ensure `trtexec` is in your PATH

## 🚀 Quick Start

### Option 1: Complete Pipeline (Recommended)
```bash
# Convert full model with testing
python run_conversion_pipeline.py \
    --weight_path weights/RTMonoDepth/full/m_640_192 \
    --model_type full \
    --test_image path/to/test/image.jpg \
    --precision fp16

# Convert small model
python run_conversion_pipeline.py \
    --weight_path weights/RTMonoDepth/s/m_640_192 \
    --model_type small \
    --test_image path/to/test/image.jpg \
    --precision fp16
```

### Option 2: Step-by-Step Conversion

#### Step 1: Convert to ONNX
```bash
# Full model
python convert_to_onnx.py \
    --weight_path weights/RTMonoDepth/full/m_640_192 \
    --output_path rtmonodepth_full.onnx \
    --model_type full \
    --verify

# Small model
python convert_to_onnx.py \
    --weight_path weights/RTMonoDepth/s/m_640_192 \
    --output_path rtmonodepth_small.onnx \
    --model_type small \
    --verify
```

#### Step 2: Convert to TensorRT (Python)
```bash
# Using Python script
python convert_to_tensorrt.py \
    --onnx_path rtmonodepth_full.onnx \
    --engine_path rtmonodepth_full_fp16.engine \
    --precision fp16 \
    --benchmark
```

#### Step 2: Convert to TensorRT (Bash script)
```bash
# Using bash script with trtexec
chmod +x trtexec_convert.sh

./trtexec_convert.sh \
    --onnx rtmonodepth_full.onnx \
    --engine rtmonodepth_full_fp16.engine \
    --precision fp16 \
    --benchmark
```

#### Step 3: Test Models
```bash
python test_onnx_tensorrt.py \
    --onnx_path rtmonodepth_full.onnx \
    --engine_path rtmonodepth_full_fp16.engine \
    --image_path path/to/test/image.jpg \
    --output_dir test_outputs
```

## 📊 Model Variants

### Available Models
1. **RTMonoDepth Full** (`weights/RTMonoDepth/full/`)
   - Higher accuracy
   - Larger model size
   - Slower inference

2. **RTMonoDepth Small** (`weights/RTMonoDepth/s/`)
   - Optimized for speed
   - Smaller model size
   - Faster inference

### Training Variants
- `m_640_192/` - Monocular training
- `ms_640_192/` - Monocular + Stereo training

## ⚙️ Precision Options

### FP32 (Full Precision)
- Highest accuracy
- Largest model size
- Slowest inference

### FP16 (Half Precision) - Recommended
- Good accuracy
- 2x smaller than FP32
- ~2x faster inference
- Supported on modern GPUs

### INT8 (8-bit Integer)
- Fastest inference
- Smallest model size
- Requires calibration for best accuracy

## 🎯 Usage Examples

### Example 1: Convert Full Model for Production
```bash
# Complete pipeline with FP16 precision
python run_conversion_pipeline.py \
    --weight_path weights/RTMonoDepth/full/m_640_192 \
    --model_type full \
    --precision fp16 \
    --output_dir ./production_models \
    --test_image sample_image.jpg
```

### Example 2: Convert Small Model for Embedded Systems
```bash
# Small model with INT8 for maximum speed
python run_conversion_pipeline.py \
    --weight_path weights/RTMonoDepth/s/m_640_192 \
    --model_type small \
    --precision int8 \
    --output_dir ./embedded_models \
    --test_image sample_image.jpg
```

### Example 3: Only ONNX Conversion
```bash
python convert_to_onnx.py \
    --weight_path weights/RTMonoDepth/full/m_640_192 \
    --output_path my_model.onnx \
    --model_type full \
    --device cpu \
    --verify
```

### Example 4: Benchmark Existing Models
```bash
python test_onnx_tensorrt.py \
    --onnx_path existing_model.onnx \
    --engine_path existing_model.engine \
    --image_path test_image.jpg
```

## 📈 Performance Expectations

### Typical Performance (RTX 3080)
| Model | Format | Precision | Inference Time | FPS |
|-------|--------|-----------|----------------|-----|
| Full | PyTorch | FP32 | ~15ms | ~67 |
| Full | ONNX | FP32 | ~12ms | ~83 |
| Full | TensorRT | FP16 | ~8ms | ~125 |
| Small | PyTorch | FP32 | ~8ms | ~125 |
| Small | ONNX | FP32 | ~6ms | ~167 |
| Small | TensorRT | FP16 | ~4ms | ~250 |

*Note: Performance varies by hardware and input size*

## 🐛 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   ```bash
   # Use CPU for conversion
   python convert_to_onnx.py --device cpu ...
   ```

2. **TensorRT Not Found**
   ```bash
   # Check TensorRT installation
   which trtexec
   # Install TensorRT if missing
   ```

3. **ONNX Runtime Issues**
   ```bash
   # Install ONNX Runtime
   pip install onnxruntime-gpu  # For GPU
   pip install onnxruntime      # For CPU
   ```

4. **Model Loading Errors**
   - Ensure weight files exist in the specified path
   - Check that encoder.pth and depth.pth are present
   - Verify model type matches the weight directory

### Performance Optimization Tips

1. **Use FP16 precision** for best speed/accuracy trade-off
2. **Optimize batch size** for your target hardware
3. **Use TensorRT** for maximum performance on NVIDIA GPUs
4. **Profile your models** to identify bottlenecks

## 📝 Output Files

After conversion, you'll have:
- `*.onnx` - ONNX model files
- `*.engine` - TensorRT engine files
- `test_outputs/` - Test inference results
- Performance benchmarks in console output

## 🔗 Integration

### Python Integration
```python
import onnxruntime as ort

# Load ONNX model
session = ort.InferenceSession("model.onnx")

# Run inference
outputs = session.run(None, {"input": input_array})
```

### TensorRT Integration
```python
import tensorrt as trt
import pycuda.driver as cuda

# Load TensorRT engine
with open("model.engine", "rb") as f:
    engine_data = f.read()

runtime = trt.Runtime(trt.Logger())
engine = runtime.deserialize_cuda_engine(engine_data)
```

## 📞 Support

For issues related to:
- **RT-MonoDepth models**: Check the original repository
- **ONNX conversion**: Verify PyTorch and ONNX versions
- **TensorRT conversion**: Check TensorRT installation and GPU compatibility
- **Performance**: Profile your specific hardware setup
