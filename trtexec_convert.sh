#!/bin/bash

# TensorRT Conversion Script using trtexec
# Converts ONNX models to TensorRT engines with various precision modes

set -e  # Exit on any error

# Default values
ONNX_PATH=""
ENGINE_PATH=""
PRECISION="fp16"
BATCH_SIZE=1
WORKSPACE_SIZE=1024
VERBOSE=false
BENCHMARK=false

# Function to display usage
usage() {
    echo "Usage: $0 --onnx <onnx_path> --engine <engine_path> [options]"
    echo ""
    echo "Required arguments:"
    echo "  --onnx <path>        Path to input ONNX model"
    echo "  --engine <path>      Path to output TensorRT engine"
    echo ""
    echo "Optional arguments:"
    echo "  --precision <mode>   Precision mode: fp32, fp16, int8 (default: fp16)"
    echo "  --batch-size <size>  Batch size for optimization (default: 1)"
    echo "  --workspace <size>   Workspace size in MB (default: 1024)"
    echo "  --verbose            Enable verbose output"
    echo "  --benchmark          Benchmark the created engine"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --onnx model.onnx --engine model.engine"
    echo "  $0 --onnx model.onnx --engine model.engine --precision fp16 --benchmark"
    echo "  $0 --onnx model.onnx --engine model.engine --precision int8 --workspace 2048"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --onnx)
            ONNX_PATH="$2"
            shift 2
            ;;
        --engine)
            ENGINE_PATH="$2"
            shift 2
            ;;
        --precision)
            PRECISION="$2"
            shift 2
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --workspace)
            WORKSPACE_SIZE="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --benchmark)
            BENCHMARK=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check required arguments
if [[ -z "$ONNX_PATH" || -z "$ENGINE_PATH" ]]; then
    echo "Error: Both --onnx and --engine arguments are required"
    usage
    exit 1
fi

# Check if ONNX file exists
if [[ ! -f "$ONNX_PATH" ]]; then
    echo "Error: ONNX file not found: $ONNX_PATH"
    exit 1
fi

# Check if trtexec is available
if ! command -v trtexec &> /dev/null; then
    echo "Error: trtexec not found. Please install TensorRT and ensure trtexec is in PATH"
    echo "For installation instructions, visit: https://developer.nvidia.com/tensorrt"
    exit 1
fi

# Validate precision mode
case $PRECISION in
    fp32|fp16|int8)
        ;;
    *)
        echo "Error: Invalid precision mode: $PRECISION"
        echo "Valid options: fp32, fp16, int8"
        exit 1
        ;;
esac

echo "=============================================="
echo "TensorRT Engine Conversion"
echo "=============================================="
echo "ONNX Model:     $ONNX_PATH"
echo "Engine Output:  $ENGINE_PATH"
echo "Precision:      $PRECISION"
echo "Batch Size:     $BATCH_SIZE"
echo "Workspace:      ${WORKSPACE_SIZE} MB"
echo "Verbose:        $VERBOSE"
echo "Benchmark:      $BENCHMARK"
echo "=============================================="

# Build trtexec command
TRTEXEC_CMD="trtexec"
TRTEXEC_CMD="$TRTEXEC_CMD --onnx=$ONNX_PATH"
TRTEXEC_CMD="$TRTEXEC_CMD --saveEngine=$ENGINE_PATH"
TRTEXEC_CMD="$TRTEXEC_CMD --workspace=$WORKSPACE_SIZE"

# Set input shapes for RT-MonoDepth (640x192 input size)
TRTEXEC_CMD="$TRTEXEC_CMD --minShapes=input:1x3x192x640"
TRTEXEC_CMD="$TRTEXEC_CMD --optShapes=input:${BATCH_SIZE}x3x192x640"
TRTEXEC_CMD="$TRTEXEC_CMD --maxShapes=input:${BATCH_SIZE}x3x192x640"

# Add precision flags
case $PRECISION in
    fp16)
        TRTEXEC_CMD="$TRTEXEC_CMD --fp16"
        ;;
    int8)
        TRTEXEC_CMD="$TRTEXEC_CMD --int8"
        echo "Warning: INT8 precision requires calibration data for optimal results"
        ;;
    fp32)
        # Default precision, no additional flags needed
        ;;
esac

# Add verbose flag if requested
if [[ "$VERBOSE" == true ]]; then
    TRTEXEC_CMD="$TRTEXEC_CMD --verbose"
fi

echo ""
echo "Running conversion command:"
echo "$TRTEXEC_CMD"
echo ""
echo "This may take several minutes..."
echo ""

# Record start time
START_TIME=$(date +%s)

# Run trtexec conversion
if eval $TRTEXEC_CMD; then
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    echo ""
    echo "✓ TensorRT engine created successfully!"
    echo "  Output: $ENGINE_PATH"
    echo "  Conversion time: ${DURATION} seconds"
    
    # Get file size
    if [[ -f "$ENGINE_PATH" ]]; then
        FILE_SIZE=$(du -h "$ENGINE_PATH" | cut -f1)
        echo "  Engine size: $FILE_SIZE"
    fi
else
    echo ""
    echo "✗ TensorRT conversion failed!"
    exit 1
fi

# Benchmark if requested
if [[ "$BENCHMARK" == true ]]; then
    echo ""
    echo "=============================================="
    echo "Benchmarking TensorRT Engine"
    echo "=============================================="
    
    BENCHMARK_CMD="trtexec --loadEngine=$ENGINE_PATH --iterations=100 --warmUp=10 --dumpProfile --separateProfileRun"
    
    echo "Running benchmark command:"
    echo "$BENCHMARK_CMD"
    echo ""
    
    if eval $BENCHMARK_CMD; then
        echo ""
        echo "✓ Benchmark completed successfully!"
    else
        echo ""
        echo "✗ Benchmark failed!"
    fi
fi

echo ""
echo "=============================================="
echo "Conversion completed!"
echo "=============================================="
echo "You can now use the TensorRT engine for inference:"
echo "  Engine file: $ENGINE_PATH"
echo ""
echo "For Python inference, use:"
echo "  - tensorrt python API"
echo "  - pycuda for memory management"
echo ""
echo "For C++ inference, use:"
echo "  - TensorRT C++ API"
echo "  - CUDA runtime"
