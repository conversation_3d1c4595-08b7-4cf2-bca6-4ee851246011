#!/usr/bin/env python3
"""
TensorRT Conversion Script for RT-MonoDepth Models
Converts ONNX models to TensorRT engines using trtexec
"""

import argparse
import os
import subprocess
import sys
import time


def check_trtexec():
    """Check if trtexec is available"""
    try:
        result = subprocess.run(['trtexec', '--help'], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False


def convert_onnx_to_tensorrt(onnx_path, engine_path, precision='fp16', 
                           batch_size=1, workspace_size=1024, verbose=False):
    """Convert ONNX model to TensorRT engine using trtexec"""
    
    if not os.path.exists(onnx_path):
        raise FileNotFoundError(f"ONNX file not found: {onnx_path}")
    
    if not check_trtexec():
        raise RuntimeError("trtexec not found. Please install TensorRT and ensure trtexec is in PATH")
    
    # Build trtexec command
    cmd = [
        'trtexec',
        f'--onnx={onnx_path}',
        f'--saveEngine={engine_path}',
        f'--workspace={workspace_size}',
        f'--minShapes=input:1x3x192x640',
        f'--optShapes=input:{batch_size}x3x192x640',
        f'--maxShapes=input:{batch_size}x3x192x640',
    ]
    
    # Add precision flags
    if precision == 'fp16':
        cmd.append('--fp16')
    elif precision == 'int8':
        cmd.append('--int8')
        print("Warning: INT8 precision requires calibration data for optimal results")
    elif precision == 'fp32':
        pass  # Default precision
    else:
        raise ValueError(f"Unsupported precision: {precision}")
    
    # Add verbose flag if requested
    if verbose:
        cmd.append('--verbose')
    
    print(f"Converting ONNX to TensorRT engine...")
    print(f"Command: {' '.join(cmd)}")
    print(f"This may take several minutes...")
    
    start_time = time.time()
    
    try:
        # Run trtexec
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
        
        if result.returncode == 0:
            end_time = time.time()
            print(f"✓ TensorRT engine created successfully: {engine_path}")
            print(f"Conversion time: {end_time - start_time:.2f} seconds")
            
            # Print some output info
            if "Engine built" in result.stdout:
                print("Engine build completed successfully")
            
            return True
        else:
            print(f"✗ TensorRT conversion failed!")
            print(f"Return code: {result.returncode}")
            print(f"STDOUT:\n{result.stdout}")
            print(f"STDERR:\n{result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ TensorRT conversion timed out (30 minutes)")
        return False
    except Exception as e:
        print(f"✗ TensorRT conversion failed with exception: {e}")
        return False


def benchmark_engine(engine_path, iterations=100, warmup=10):
    """Benchmark the TensorRT engine"""
    if not os.path.exists(engine_path):
        print(f"Engine file not found: {engine_path}")
        return False
    
    cmd = [
        'trtexec',
        f'--loadEngine={engine_path}',
        f'--iterations={iterations}',
        f'--warmUp={warmup}',
        '--dumpProfile',
        '--separateProfileRun'
    ]
    
    print(f"Benchmarking TensorRT engine...")
    print(f"Running {iterations} iterations with {warmup} warmup iterations")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ Benchmark completed successfully")
            
            # Extract performance metrics from output
            lines = result.stdout.split('\n')
            for line in lines:
                if 'mean' in line.lower() and ('ms' in line or 'fps' in line):
                    print(f"Performance: {line.strip()}")
            
            return True
        else:
            print(f"✗ Benchmark failed!")
            print(f"STDERR:\n{result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Benchmark timed out")
        return False
    except Exception as e:
        print(f"✗ Benchmark failed with exception: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Convert ONNX models to TensorRT engines')
    parser.add_argument('--onnx_path', type=str, required=True,
                        help='Path to input ONNX model')
    parser.add_argument('--engine_path', type=str, required=True,
                        help='Path to output TensorRT engine')
    parser.add_argument('--precision', type=str, choices=['fp32', 'fp16', 'int8'], 
                        default='fp16', help='Precision mode')
    parser.add_argument('--batch_size', type=int, default=1,
                        help='Batch size for optimization')
    parser.add_argument('--workspace_size', type=int, default=1024,
                        help='Workspace size in MB')
    parser.add_argument('--benchmark', action='store_true',
                        help='Benchmark the created engine')
    parser.add_argument('--benchmark_iterations', type=int, default=100,
                        help='Number of benchmark iterations')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    
    args = parser.parse_args()
    
    print(f"TensorRT Conversion Settings:")
    print(f"  ONNX Model: {args.onnx_path}")
    print(f"  Engine Output: {args.engine_path}")
    print(f"  Precision: {args.precision}")
    print(f"  Batch Size: {args.batch_size}")
    print(f"  Workspace: {args.workspace_size} MB")
    print()
    
    # Convert ONNX to TensorRT
    success = convert_onnx_to_tensorrt(
        args.onnx_path, 
        args.engine_path, 
        args.precision,
        args.batch_size,
        args.workspace_size,
        args.verbose
    )
    
    if not success:
        sys.exit(1)
    
    # Benchmark if requested
    if args.benchmark:
        print()
        benchmark_engine(args.engine_path, args.benchmark_iterations)
    
    print("\nConversion completed!")


if __name__ == '__main__':
    main()
