#!/usr/bin/env python3
"""
Complete conversion pipeline for RT-MonoDepth models
Converts PyTorch -> ONNX -> TensorRT and tests all formats
"""

import argparse
import os
import sys
import subprocess
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed with return code {e.returncode}")
        return False
    except Exception as e:
        print(f"✗ {description} failed with exception: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Complete RT-MonoDepth conversion pipeline')
    parser.add_argument('--weight_path', type=str, required=True,
                        help='Path to model weights directory (e.g., weights/RTMonoDepth/full/m_640_192)')
    parser.add_argument('--model_type', type=str, choices=['full', 'small'], default='full',
                        help='Model type: full or small')
    parser.add_argument('--output_dir', type=str, default='./converted_models',
                        help='Output directory for converted models')
    parser.add_argument('--test_image', type=str, 
                        help='Path to test image for validation')
    parser.add_argument('--precision', type=str, choices=['fp32', 'fp16', 'int8'], 
                        default='fp16', help='TensorRT precision mode')
    parser.add_argument('--skip_onnx', action='store_true',
                        help='Skip ONNX conversion (use existing ONNX file)')
    parser.add_argument('--skip_tensorrt', action='store_true',
                        help='Skip TensorRT conversion')
    parser.add_argument('--skip_test', action='store_true',
                        help='Skip testing phase')
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Define file paths
    model_name = f"rtmonodepth_{args.model_type}_{args.precision}"
    onnx_path = output_dir / f"{model_name}.onnx"
    engine_path = output_dir / f"{model_name}.engine"
    
    print(f"RT-MonoDepth Conversion Pipeline")
    print(f"Model Type: {args.model_type}")
    print(f"Weight Path: {args.weight_path}")
    print(f"Output Directory: {output_dir}")
    print(f"ONNX Path: {onnx_path}")
    print(f"Engine Path: {engine_path}")
    print(f"Precision: {args.precision}")
    
    # Check if weight path exists
    if not os.path.exists(args.weight_path):
        print(f"Error: Weight path does not exist: {args.weight_path}")
        sys.exit(1)
    
    success_count = 0
    total_steps = 0
    
    # Step 1: Convert to ONNX
    if not args.skip_onnx:
        total_steps += 1
        cmd = [
            sys.executable, 'convert_to_onnx.py',
            '--weight_path', args.weight_path,
            '--output_path', str(onnx_path),
            '--model_type', args.model_type,
            '--verify'
        ]
        
        if run_command(cmd, "PyTorch to ONNX conversion"):
            success_count += 1
        else:
            print("ONNX conversion failed. Stopping pipeline.")
            sys.exit(1)
    else:
        if onnx_path.exists():
            print(f"Using existing ONNX file: {onnx_path}")
        else:
            print(f"Error: ONNX file not found: {onnx_path}")
            sys.exit(1)
    
    # Step 2: Convert to TensorRT
    if not args.skip_tensorrt:
        total_steps += 1
        cmd = [
            sys.executable, 'convert_to_tensorrt.py',
            '--onnx_path', str(onnx_path),
            '--engine_path', str(engine_path),
            '--precision', args.precision,
            '--benchmark'
        ]
        
        if run_command(cmd, "ONNX to TensorRT conversion"):
            success_count += 1
        else:
            print("TensorRT conversion failed. Continuing with available models.")
    
    # Step 3: Test models
    if not args.skip_test and args.test_image:
        total_steps += 1
        test_output_dir = output_dir / "test_outputs"
        
        cmd = [
            sys.executable, 'test_onnx_tensorrt.py',
            '--image_path', args.test_image,
            '--output_dir', str(test_output_dir)
        ]
        
        if onnx_path.exists():
            cmd.extend(['--onnx_path', str(onnx_path)])
        
        if engine_path.exists():
            cmd.extend(['--engine_path', str(engine_path)])
        
        if run_command(cmd, "Model testing and validation"):
            success_count += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"CONVERSION PIPELINE SUMMARY")
    print('='*60)
    print(f"Completed steps: {success_count}/{total_steps}")
    
    if onnx_path.exists():
        print(f"✓ ONNX model: {onnx_path}")
        print(f"  Size: {onnx_path.stat().st_size / (1024*1024):.2f} MB")
    
    if engine_path.exists():
        print(f"✓ TensorRT engine: {engine_path}")
        print(f"  Size: {engine_path.stat().st_size / (1024*1024):.2f} MB")
    
    if args.test_image and not args.skip_test:
        test_output_dir = output_dir / "test_outputs"
        if test_output_dir.exists():
            print(f"✓ Test outputs: {test_output_dir}")
    
    print(f"\nNext steps:")
    print(f"1. Use the ONNX model with: onnxruntime")
    print(f"2. Use the TensorRT engine with: tensorrt python API")
    print(f"3. Deploy on embedded systems (Jetson, etc.)")
    
    if success_count == total_steps:
        print(f"\n🎉 All conversions completed successfully!")
    else:
        print(f"\n⚠️  Some conversions failed. Check the logs above.")


if __name__ == '__main__':
    main()
