#!/usr/bin/env python3
"""
ONNX Conversion Script for RT-MonoDepth Models
Converts both RTMonoDepth (full) and RTMonoDepth_s (small) models to ONNX format
"""

import argparse
import os
import torch
import torch.onnx
import numpy as np
from networks.RTMonoDepth.RTMonoDepth import Depth<PERSON>ecoder as FullDepthDecoder, DepthEncoder as FullDepthEncoder
from networks.RTMonoDepth.RTMonoDepth_s import DepthDecoder as SmallDepthDecoder, DepthEncoder as SmallDepthEncoder


class RTMonoDepthFull(torch.nn.Module):
    """Combined model for RTMonoDepth Full version"""
    def __init__(self, encoder, decoder):
        super(RTMonoDepthFull, self).__init__()
        self.encoder = encoder
        self.decoder = decoder
    
    def forward(self, x):
        features = self.encoder(x)
        outputs = self.decoder(features)
        return outputs[("disp", 0)]


class RTMonoDepthSmall(torch.nn.Module):
    """Combined model for RTMonoDepth Small version"""
    def __init__(self, encoder, decoder):
        super(RTMonoDepthSmall, self).__init__()
        self.encoder = encoder
        self.decoder = decoder
    
    def forward(self, x):
        features = self.encoder(x)
        outputs = self.decoder(features)
        return outputs[("disp", 0)]


def load_model(weight_path, model_type="full", device="cuda"):
    """Load the pretrained model"""
    encoder_path = os.path.join(weight_path, "encoder.pth")
    depth_decoder_path = os.path.join(weight_path, "depth.pth")
    
    if not os.path.exists(encoder_path) or not os.path.exists(depth_decoder_path):
        raise FileNotFoundError(f"Model weights not found in {weight_path}")
    
    # Load encoder
    if model_type == "full":
        encoder = FullDepthEncoder()
        decoder_class = FullDepthDecoder
        model_class = RTMonoDepthFull
    else:  # small
        encoder = SmallDepthEncoder()
        decoder_class = SmallDepthDecoder
        model_class = RTMonoDepthSmall
    
    loaded_dict_enc = torch.load(encoder_path, map_location=device)
    filtered_dict_enc = {k: v for k, v in loaded_dict_enc.items() if k in encoder.state_dict()}
    encoder.load_state_dict(filtered_dict_enc)
    
    # Load decoder
    decoder = decoder_class(num_ch_enc=encoder.num_ch_enc)
    loaded_dict_dec = torch.load(depth_decoder_path, map_location=device)
    decoder.load_state_dict(loaded_dict_dec)
    
    # Create combined model
    model = model_class(encoder, decoder)
    model.to(device)
    model.eval()
    
    # Get input dimensions from encoder weights
    height = loaded_dict_enc.get('height', 192)
    width = loaded_dict_enc.get('width', 640)
    
    return model, height, width


def convert_to_onnx(model, height, width, output_path, device="cuda"):
    """Convert PyTorch model to ONNX"""
    # Create dummy input
    dummy_input = torch.randn(1, 3, height, width).to(device)
    
    # Export to ONNX
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )
    
    print(f"Model exported to {output_path}")
    print(f"Input shape: [batch_size, 3, {height}, {width}]")
    print(f"Output shape: [batch_size, 1, {height}, {width}]")


def verify_onnx_model(onnx_path, height, width, device="cuda"):
    """Verify the ONNX model can be loaded and run"""
    try:
        import onnxruntime as ort
        
        # Create ONNX Runtime session
        ort_session = ort.InferenceSession(onnx_path)
        
        # Create dummy input
        dummy_input = np.random.randn(1, 3, height, width).astype(np.float32)
        
        # Run inference
        ort_inputs = {ort_session.get_inputs()[0].name: dummy_input}
        ort_outputs = ort_session.run(None, ort_inputs)
        
        print(f"ONNX model verification successful!")
        print(f"Output shape: {ort_outputs[0].shape}")
        return True
        
    except ImportError:
        print("Warning: onnxruntime not installed. Skipping verification.")
        print("Install with: pip install onnxruntime")
        return False
    except Exception as e:
        print(f"ONNX model verification failed: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Convert RT-MonoDepth models to ONNX')
    parser.add_argument('--weight_path', type=str, required=True,
                        help='Path to model weights directory')
    parser.add_argument('--output_path', type=str, required=True,
                        help='Output path for ONNX model')
    parser.add_argument('--model_type', type=str, choices=['full', 'small'], default='full',
                        help='Model type: full or small')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use for conversion')
    parser.add_argument('--verify', action='store_true',
                        help='Verify the converted ONNX model')
    
    args = parser.parse_args()
    
    # Check if CUDA is available
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        args.device = 'cpu'
    
    print(f"Converting {args.model_type} model from {args.weight_path}")
    print(f"Using device: {args.device}")
    
    # Load model
    model, height, width = load_model(args.weight_path, args.model_type, args.device)
    
    # Convert to ONNX
    convert_to_onnx(model, height, width, args.output_path, args.device)
    
    # Verify if requested
    if args.verify:
        verify_onnx_model(args.output_path, height, width, args.device)
    
    print("Conversion completed!")


if __name__ == '__main__':
    main()
